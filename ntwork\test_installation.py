# -*- coding: utf-8 -*-
"""
测试ntwork安装是否成功
"""
import sys

def test_import():
    """测试导入ntwork模块"""
    try:
        import ntwork
        print("✅ ntwork模块导入成功")
        print(f"版本信息: {getattr(ntwork, '__version__', '未知版本')}")
        return True
    except ImportError as e:
        print(f"❌ ntwork模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        import ntwork
        
        # 测试创建WeWork实例
        wework = ntwork.WeWork()
        print("✅ WeWork实例创建成功")
        
        # 测试常量是否存在
        constants = [
            'MT_RECV_TEXT_MSG',
            'MT_ALL'
        ]
        
        for const in constants:
            if hasattr(ntwork, const):
                print(f"✅ 常量 {const} 存在")
            else:
                print(f"⚠️  常量 {const} 不存在")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    print("开始测试ntwork安装...")
    print("-" * 40)
    
    # 测试Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 6):
        print("⚠️  警告: ntwork需要Python 3.6或更高版本")
    else:
        print("✅ Python版本符合要求")
    
    print("-" * 40)
    
    # 测试导入
    if not test_import():
        print("\n安装建议:")
        print("1. 确保已安装ntwork: pip install ntwork")
        print("2. 或者从源码安装: python setup.py install")
        return False
    
    print("-" * 40)
    
    # 测试基本功能
    if not test_basic_functionality():
        print("\n可能的问题:")
        print("1. ntwork版本不兼容")
        print("2. 缺少必要的依赖")
        return False
    
    print("-" * 40)
    print("🎉 所有测试通过！ntwork安装成功")
    print("\n接下来您可以:")
    print("1. 运行 python auto_reply_hello.py 开始简单的自动回复")
    print("2. 运行 python auto_reply_advanced.py 使用高级功能")
    print("3. 查看 自动回复使用说明.md 了解详细使用方法")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
