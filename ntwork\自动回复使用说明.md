# 企业微信自动回复机器人使用说明

## 功能介绍

我为您创建了两个自动回复脚本：

### 1. 基础版本 (`auto_reply_hello.py`)
- 简单易用，收到私聊消息后自动回复"你好啊"
- 不会回复群消息和自己发送的消息
- 适合快速测试和简单使用

### 2. 高级版本 (`auto_reply_advanced.py`)
- 支持更多自定义配置
- 可以设置回复内容、时间限制、关键词过滤等
- 支持群消息回复（可配置）
- 支持回复频率限制

## 使用前准备

### 1. 安装依赖
```bash
# 进入项目目录
cd ntwork

# 安装ntwork库
pip install ntwork
```

### 2. 确保企业微信版本正确
- 下载并安装指定版本的企业微信: [WeCom_4.0.8.6027.exe](https://dldir1.qq.com/wework/work_weixin/WeCom_4.0.8.6027.exe)
- 确保企业微信已经登录

## 使用方法

### 基础版本使用
```bash
python auto_reply_hello.py
```

运行后：
1. 程序会自动打开企业微信
2. 等待登录完成
3. 开始监听消息
4. 收到私聊消息后自动回复"你好啊"

### 高级版本使用
```bash
python auto_reply_advanced.py
```

#### 自定义配置
编辑 `auto_reply_advanced.py` 文件中的配置：

```python
# 修改回复内容
bot.config["reply_message"] = "你好啊，我是自动回复机器人"

# 启用群消息回复
bot.config["reply_to_groups"] = True

# 设置忽略关键词（包含这些词的消息不回复）
bot.config["ignore_keywords"] = ["广告", "推广", "spam"]

# 设置只回复特定关键词（只回复包含这些词的消息）
bot.config["only_reply_keywords"] = ["你好", "hello"]

# 启用时间限制（只在工作时间回复）
bot.config["enable_time_limit"] = True
bot.config["start_time"] = "09:00"
bot.config["end_time"] = "18:00"

# 设置回复间隔（避免频繁回复同一个人）
bot.config["reply_interval"] = 60  # 60秒内不重复回复同一个会话
```

## 功能特性

### 基础版本特性
- ✅ 自动回复私聊消息
- ✅ 忽略群消息
- ✅ 忽略自己发送的消息
- ✅ 简单易用

### 高级版本特性
- ✅ 自定义回复内容
- ✅ 可选择回复私聊/群消息
- ✅ 关键词过滤功能
- ✅ 时间限制功能
- ✅ 回复频率限制
- ✅ 详细的日志输出
- ✅ 灵活的配置选项

## 注意事项

1. **企业微信版本**: 必须使用指定版本的企业微信客户端
2. **登录状态**: 确保企业微信已经登录
3. **权限问题**: 程序需要访问企业微信，可能需要管理员权限
4. **网络连接**: 确保网络连接正常
5. **防火墙**: 可能需要允许程序通过防火墙

## 常见问题

### Q: 程序启动后没有反应？
A: 检查企业微信是否已经登录，版本是否正确

### Q: 收到消息但没有自动回复？
A: 检查是否为群消息（基础版本不回复群消息），或者检查配置设置

### Q: 如何停止程序？
A: 按 `Ctrl+C` 组合键退出程序

### Q: 如何修改回复内容？
A: 编辑脚本文件中的 `reply_message` 配置项

## 扩展功能

如果您需要更复杂的功能，可以基于现有代码进行扩展：

- 智能回复（接入AI API）
- 多种消息类型支持（图片、文件等）
- 数据库记录聊天历史
- Web管理界面
- 定时任务功能

## 技术支持

如果遇到问题，可以：
1. 查看项目的 [FAQ文档](docs/FAQ.md)
2. 参考更多 [示例代码](examples/)
3. 加入 [NtWork交流群](https://jq.qq.com/?_wv=1027&k=y8d0wpXJ)

## 免责声明

本工具仅供学习和研究使用，请遵守相关法律法规和企业微信的使用条款。使用本工具产生的任何后果由使用者自行承担。
