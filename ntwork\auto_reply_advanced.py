# -*- coding: utf-8 -*-
"""
高级自动回复机器人
支持自定义回复内容、群消息回复、关键词过滤等功能
"""
import sys
import time
import ntwork
from datetime import datetime

class AutoReplyBot:
    def __init__(self):
        self.wework = ntwork.WeWork()
        
        # 配置选项
        self.config = {
            # 基本回复设置
            "reply_message": "你好啊",  # 自动回复的内容
            "reply_to_groups": False,   # 是否回复群消息
            "reply_to_private": True,   # 是否回复私聊消息
            
            # 过滤设置
            "ignore_keywords": [],      # 包含这些关键词的消息不回复
            "only_reply_keywords": [],  # 只回复包含这些关键词的消息(为空则回复所有)
            
            # 时间限制
            "enable_time_limit": False, # 是否启用时间限制
            "start_time": "09:00",      # 开始自动回复时间
            "end_time": "18:00",        # 结束自动回复时间
            
            # 频率限制
            "reply_interval": 0,        # 回复间隔(秒)，0表示无限制
        }
        
        self.last_reply_time = {}  # 记录每个会话的最后回复时间
        
    def is_in_time_range(self):
        """检查当前时间是否在允许回复的时间范围内"""
        if not self.config["enable_time_limit"]:
            return True
            
        now = datetime.now().strftime("%H:%M")
        start_time = self.config["start_time"]
        end_time = self.config["end_time"]
        
        return start_time <= now <= end_time
    
    def should_reply(self, content, conversation_id):
        """判断是否应该回复这条消息"""
        # 检查时间限制
        if not self.is_in_time_range():
            return False, "不在允许回复的时间范围内"
        
        # 检查频率限制
        if self.config["reply_interval"] > 0:
            last_time = self.last_reply_time.get(conversation_id, 0)
            current_time = time.time()
            if current_time - last_time < self.config["reply_interval"]:
                return False, f"距离上次回复时间不足{self.config['reply_interval']}秒"
        
        # 检查忽略关键词
        for keyword in self.config["ignore_keywords"]:
            if keyword in content:
                return False, f"消息包含忽略关键词: {keyword}"
        
        # 检查只回复关键词
        if self.config["only_reply_keywords"]:
            found_keyword = False
            for keyword in self.config["only_reply_keywords"]:
                if keyword in content:
                    found_keyword = True
                    break
            if not found_keyword:
                return False, "消息不包含指定的回复关键词"
        
        return True, "符合回复条件"
    
    def start(self):
        """启动自动回复机器人"""
        print("正在启动企业微信...")
        self.wework.open(smart=True)
        
        print("等待登录...")
        self.wework.wait_login()
        
        login_info = self.wework.get_login_info()
        print(f"登录成功！当前用户: {login_info.get('name', '未知用户')}")
        
        # 注册消息回调
        @self.wework.msg_register(ntwork.MT_RECV_TEXT_MSG)
        def on_recv_text_msg(wework_instance: ntwork.WeWork, message):
            try:
                data = message["data"]
                sender_user_id = data["sender"]
                self_user_id = wework_instance.get_login_info()["user_id"]
                conversation_id: str = data["conversation_id"]
                content = data.get("content", "")
                
                print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 收到消息")
                print(f"内容: {content}")
                print(f"发送者: {sender_user_id}")
                
                # 不回复自己的消息
                if sender_user_id == self_user_id:
                    print("跳过: 这是自己发送的消息")
                    return
                
                # 判断消息类型
                is_group = conversation_id.startswith("R:")
                if is_group:
                    print("消息类型: 群消息")
                    if not self.config["reply_to_groups"]:
                        print("跳过: 配置为不回复群消息")
                        return
                else:
                    print("消息类型: 私聊消息")
                    if not self.config["reply_to_private"]:
                        print("跳过: 配置为不回复私聊消息")
                        return
                
                # 检查是否应该回复
                should_reply, reason = self.should_reply(content, conversation_id)
                if not should_reply:
                    print(f"跳过回复: {reason}")
                    return
                
                # 发送回复
                reply_content = self.config["reply_message"]
                wework_instance.send_text(
                    conversation_id=conversation_id,
                    content=reply_content
                )
                
                # 记录回复时间
                self.last_reply_time[conversation_id] = time.time()
                
                print(f"✅ 已自动回复: {reply_content}")
                
            except Exception as e:
                print(f"❌ 处理消息时出错: {e}")
        
        self.print_config()
        print("自动回复机器人已启动！按 Ctrl+C 退出程序")
        print("-" * 60)
        
        # 保持程序运行
        try:
            while True:
                time.sleep(0.5)
        except KeyboardInterrupt:
            print("\n正在退出程序...")
            ntwork.exit_()
            sys.exit()
    
    def print_config(self):
        """打印当前配置"""
        print("\n当前配置:")
        print(f"- 回复内容: {self.config['reply_message']}")
        print(f"- 回复私聊: {'是' if self.config['reply_to_private'] else '否'}")
        print(f"- 回复群消息: {'是' if self.config['reply_to_groups'] else '否'}")
        
        if self.config["ignore_keywords"]:
            print(f"- 忽略关键词: {', '.join(self.config['ignore_keywords'])}")
        
        if self.config["only_reply_keywords"]:
            print(f"- 只回复关键词: {', '.join(self.config['only_reply_keywords'])}")
        
        if self.config["enable_time_limit"]:
            print(f"- 工作时间: {self.config['start_time']} - {self.config['end_time']}")
        
        if self.config["reply_interval"] > 0:
            print(f"- 回复间隔: {self.config['reply_interval']}秒")
        
        print("-" * 60)

def main():
    # 创建机器人实例
    bot = AutoReplyBot()
    
    # 可以在这里修改配置
    # bot.config["reply_message"] = "你好啊，我是自动回复机器人"
    # bot.config["reply_to_groups"] = True  # 如果要回复群消息
    # bot.config["ignore_keywords"] = ["广告", "推广"]  # 忽略包含这些词的消息
    # bot.config["enable_time_limit"] = True  # 启用时间限制
    
    # 启动机器人
    bot.start()

if __name__ == "__main__":
    main()
